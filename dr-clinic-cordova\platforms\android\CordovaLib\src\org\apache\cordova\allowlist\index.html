<!--
         Licensed to the Apache Software Foundation (ASF) under one
         or more contributor license agreements.  See the NOTICE file
         distributed with this work for additional information
         regarding copyright ownership.  The ASF licenses this file
         to you under the Apache License, Version 2.0 (the
         "License"); you may not use this file except in compliance
         with the License.  You may obtain a copy of the License at

           http://www.apache.org/licenses/LICENSE-2.0

         Unless required by applicable law or agreed to in writing,
         software distributed under the License is distributed on an
         "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
         KIND, either express or implied.  See the License for the
         specific language governing permissions and limitations
         under the License.
-->
<!DOCTYPE HTML>
<html>
  <head>
    <meta name="viewport" content="width=320, user-scalable=no" />
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title>Cordova Tests</title>
      <link rel="stylesheet" href="../master.css" type="text/css" media="screen" title="no title">
      <script type="text/javascript" charset="utf-8" src="../cordova.js"></script>
      <script type="text/javascript" charset="utf-8" src="../main.js"></script>
  </head>
  <body onload="init();" id="stage" class="theme">
    <h1>Allow List Page 1</h1>
    <div id="info">
        <h4>Cordova: <span id="cordova"> &nbsp;</span></h4>
        <h4>Deviceready: <span id="deviceready"> &nbsp;</span></h4>
     </div>
     <div id="info">
     Loading Page 2 should be successful.<br>
     Loading Page 3 should be in web browser.<br>
     Loading Page 2 with target=_blank should be in web browser? <br>
     (THIS DOESN'T HAPPEN.) https://issues.apache.org/jira/browse/CB-362 
     </div>
    <a href="index2.html" class="btn large">Page 2</a>
    <a href="http://www.google.com" class="btn large">Page 3</a>
    <a href="index2.html" class="btn large" target="_blank">Page 2 with target=_blank</a>
  </body>
</html>
