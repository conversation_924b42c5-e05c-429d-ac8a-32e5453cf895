-- Merging decision tree log ---
manifest
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
MERGED from [:CordovaLib] D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:1-29:12
MERGED from [androidx.appcompat:appcompat:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\677d6bfd2a0fa81a5ea7d384ddaf69c3\transformed\appcompat-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7c26442ee087d1f53354e5224cef36f5\transformed\jetified-core-splashscreen-1.0.0-rc01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\71d075d61be28263b04978ee3f159a4a\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\ff498f37100d6e1fe491af3774971b79\transformed\jetified-activity-1.2.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\235dad04a7026b3604863fea495f2885\transformed\jetified-appcompat-resources-1.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8883e15f9250c8a60de86bd87cfc59e1\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\65232173b04971703cd26ba805931d3c\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d13416086e3f06fa514a9ea6f0ef11a7\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a777fe61f2fb6f288f006ba2dc831ca\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf93d997b79a98042b32594f5d504e94\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1aff2ddf01568eb9c920c62dc5ae6069\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e455c3ea90ad376c03090ce9dc374ef8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1994ecb88b863e567889662d6d1577ff\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7e5727da935322e877e6699d5426a2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\61a00261fd6401a02e9ff2b118a973a2\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c1253ba0f01d41a61818f038e2c53e6\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6fe9cfa0b29200084697164cfb74c7a\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10c74d8f3b1b6c997fc44116174b4fa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc1b69e07401149993cb376e16baf0b4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\619fb6ba05272da3e621378d0fbc7b35\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c00a1c3afc9542b0ae345b16f746ea13\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\64a995173f0388e397c678fcddf60938\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\69e6e62a76ab941180a488ccd6a2908d\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba493829f57f193f643ffd6d76691fa0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\911a9585ae97fe570a3620766fd03de9\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:1-13:12
	package
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:102-146
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
	android:versionName
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:74-101
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
	android:hardwareAccelerated
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:11-45
	xmlns:android
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:147-205
	android:versionCode
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:2:46-73
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
supports-screens
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
	android:largeScreens
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
	android:smallScreens
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
	android:normalScreens
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
	android:xlargeScreens
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
	android:resizeable
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
	android:anyDensity
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
uses-permission#android.permission.INTERNET
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:4:22-64
application
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:5-12:19
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10c74d8f3b1b6c997fc44116174b4fa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10c74d8f3b1b6c997fc44116174b4fa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:131-157
	android:label
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:98-130
	android:hardwareAccelerated
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:18-52
	android:icon
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:53-97
activity#com.drclinic.appointmentmanager.v2.MainActivity
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:9-11:20
	android:label
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:160-197
	android:launchMode
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:198-228
	android:windowSoftInputMode
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:303-345
	android:exported
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:136-159
	android:configChanges
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:19-135
	android:theme
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:257-302
	android:name
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:229-256
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:7:13-10:29
	android:label
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:7:28-65
action#android.intent.action.MAIN
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:8:17-69
	android:name
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:8:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:9:17-77
	android:name
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:9:27-74
uses-sdk
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
MERGED from [:CordovaLib] D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:5-27:41
MERGED from [:CordovaLib] D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:5-27:41
MERGED from [androidx.appcompat:appcompat:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\677d6bfd2a0fa81a5ea7d384ddaf69c3\transformed\appcompat-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\677d6bfd2a0fa81a5ea7d384ddaf69c3\transformed\appcompat-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7c26442ee087d1f53354e5224cef36f5\transformed\jetified-core-splashscreen-1.0.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7c26442ee087d1f53354e5224cef36f5\transformed\jetified-core-splashscreen-1.0.0-rc01\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\71d075d61be28263b04978ee3f159a4a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\71d075d61be28263b04978ee3f159a4a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\ff498f37100d6e1fe491af3774971b79\transformed\jetified-activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-3\ff498f37100d6e1fe491af3774971b79\transformed\jetified-activity-1.2.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\235dad04a7026b3604863fea495f2885\transformed\jetified-appcompat-resources-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\235dad04a7026b3604863fea495f2885\transformed\jetified-appcompat-resources-1.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8883e15f9250c8a60de86bd87cfc59e1\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8883e15f9250c8a60de86bd87cfc59e1\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\65232173b04971703cd26ba805931d3c\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\65232173b04971703cd26ba805931d3c\transformed\jetified-emoji2-views-helper-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d13416086e3f06fa514a9ea6f0ef11a7\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\d13416086e3f06fa514a9ea6f0ef11a7\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a777fe61f2fb6f288f006ba2dc831ca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a777fe61f2fb6f288f006ba2dc831ca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf93d997b79a98042b32594f5d504e94\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf93d997b79a98042b32594f5d504e94\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1aff2ddf01568eb9c920c62dc5ae6069\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1aff2ddf01568eb9c920c62dc5ae6069\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e455c3ea90ad376c03090ce9dc374ef8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e455c3ea90ad376c03090ce9dc374ef8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1994ecb88b863e567889662d6d1577ff\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1994ecb88b863e567889662d6d1577ff\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7e5727da935322e877e6699d5426a2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7e5727da935322e877e6699d5426a2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\61a00261fd6401a02e9ff2b118a973a2\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\61a00261fd6401a02e9ff2b118a973a2\transformed\jetified-lifecycle-viewmodel-savedstate-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c1253ba0f01d41a61818f038e2c53e6\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c1253ba0f01d41a61818f038e2c53e6\transformed\jetified-savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6fe9cfa0b29200084697164cfb74c7a\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6fe9cfa0b29200084697164cfb74c7a\transformed\lifecycle-viewmodel-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10c74d8f3b1b6c997fc44116174b4fa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\10c74d8f3b1b6c997fc44116174b4fa1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc1b69e07401149993cb376e16baf0b4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc1b69e07401149993cb376e16baf0b4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\619fb6ba05272da3e621378d0fbc7b35\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\619fb6ba05272da3e621378d0fbc7b35\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c00a1c3afc9542b0ae345b16f746ea13\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\c00a1c3afc9542b0ae345b16f746ea13\transformed\lifecycle-livedata-core-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\64a995173f0388e397c678fcddf60938\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\64a995173f0388e397c678fcddf60938\transformed\lifecycle-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\69e6e62a76ab941180a488ccd6a2908d\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\69e6e62a76ab941180a488ccd6a2908d\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba493829f57f193f643ffd6d76691fa0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba493829f57f193f643ffd6d76691fa0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\911a9585ae97fe570a3620766fd03de9\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\911a9585ae97fe570a3620766fd03de9\transformed\jetified-annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66d7d796b2ee701418c341a4f5e76664\transformed\jetified-startup-runtime-1.0.0\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:30:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
