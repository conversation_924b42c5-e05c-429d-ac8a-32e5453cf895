1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.drclinic.appointmentmanager"
4    android:hardwareAccelerated="true"
5    android:versionCode="10000"
6    android:versionName="1.0.0" >
7
8    <uses-sdk
9        android:minSdkVersion="22"
9-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
10        android:targetSdkVersion="33" />
10-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml
11
12    <supports-screens
12-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:5-191
13        android:anyDensity="true"
13-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:23-48
14        android:largeScreens="true"
14-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:49-76
15        android:normalScreens="true"
15-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:77-105
16        android:resizeable="true"
16-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:106-131
17        android:smallScreens="true"
17-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:132-159
18        android:xlargeScreens="true" />
18-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:3:160-188
19
20    <uses-permission android:name="android.permission.INTERNET" />
20-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:4:5-67
20-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:4:22-64
21
22    <application
22-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:5-12:19
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bae5463ffdf03e7b72e5536b9e729a62\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
24        android:debuggable="true"
25        android:hardwareAccelerated="true"
25-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:18-52
26        android:icon="@drawable/ic_cdv_splashscreen"
26-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:53-97
27        android:label="@string/app_name"
27-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:98-130
28        android:supportsRtl="true" >
28-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:5:131-157
29        <activity
29-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:9-11:20
30            android:name="com.drclinic.appointmentmanager.MainActivity"
30-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:229-256
31            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
31-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:19-135
32            android:exported="true"
32-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:136-159
33            android:label="@string/activity_name"
33-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:160-197
34            android:launchMode="singleTop"
34-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:198-228
35            android:theme="@style/Theme.App.SplashScreen"
35-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:257-302
36            android:windowSoftInputMode="adjustResize" >
36-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:6:303-345
37            <intent-filter android:label="@string/launcher_name" >
37-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:7:13-10:29
37-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:7:28-65
38                <action android:name="android.intent.action.MAIN" />
38-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:8:17-69
38-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:8:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:9:17-77
40-->D:\dr\dr-clinic-cordova\platforms\android\app\src\main\AndroidManifest.xml:9:27-74
41            </intent-filter>
42        </activity>
43
44        <provider
44-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
45            android:name="androidx.startup.InitializationProvider"
45-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
46            android:authorities="com.drclinic.appointmentmanager.androidx-startup"
46-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
47            android:exported="false" >
47-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
48            <meta-data
48-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
49                android:name="androidx.emoji2.text.EmojiCompatInitializer"
49-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
50                android:value="androidx.startup" />
50-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e673d1a98e302a686fedbbd48ab550ad\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
51            <meta-data
51-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
52                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
52-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
53                android:value="androidx.startup" />
53-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3132b62b958c22f6a02f594e95048346\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
54        </provider>
55    </application>
56
57</manifest>
