-- Merging decision tree log ---
manifest
ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:20:1-22:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:20:1-22:12
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:20:1-22:12
	package
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:21:7-35
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
	android:versionName
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:21:36-61
	xmlns:android
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:20:11-69
	android:versionCode
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml:21:62-85
uses-sdk
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
		ADDED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
		INJECTED from D:\dr\dr-clinic-cordova\platforms\android\CordovaLib\AndroidManifest.xml
